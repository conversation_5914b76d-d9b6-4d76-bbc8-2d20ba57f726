//
//  ImageManager.swift
//  practice
//
//  Created by <PERSON> on 2025/4/22.
//

import Foundation
import SwiftUI
import SwiftData

class ImageManager: ObservableObject {
    private let folderName = "Images"
    private let compressionQuality: CGFloat = 1.0
    private let modelContext: ModelContext
    let storage: ImageStorageProvider
    
    // Cache system to avoid loading the same image multiple times
    var imageCache = NSCache<NSString, UIImage>()
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
        self.storage = ImageManager.defaultStorageProvider()
        self.imageCache = NSCache<NSString, UIImage>()

        // 根据设备内存动态调整缓存限制
        let memoryLimit = ProcessInfo.processInfo.physicalMemory
        if memoryLimit > 4 * 1024 * 1024 * 1024 { // 4GB+
            imageCache.countLimit = 100
            imageCache.totalCostLimit = 100 * 1024 * 1024 // 100MB
        } else if memoryLimit > 2 * 1024 * 1024 * 1024 { // 2GB+
            imageCache.countLimit = 75
            imageCache.totalCostLimit = 75 * 1024 * 1024 // 75MB
        } else {
            imageCache.countLimit = 50
            imageCache.totalCostLimit = 50 * 1024 * 1024 // 50MB
        }

        // 设置缓存清理策略
        imageCache.evictsObjectsWithDiscardedContent = true
    }
    
    // MARK: - Image Management Methods
    
    /// Saves an image and updates the entity with the file path
    func saveImage<T: PersistentModel>(_ image: UIImage, for entity: T, pathKeyPath: ReferenceWritableKeyPath<T, String?>, completion: ((Result<String, Error>) -> Void)? = nil) {
        // 获取旧的图片路径（如果有）
        let oldPath = entity[keyPath: pathKeyPath]

        let uniqueId = UUID().uuidString
        let fileName = "img-\(uniqueId).jpg"

        // 先裁剪掉四周 1 像素
        let croppedImage = cropEdges(of: image, by: 1)

        // 然后转为 JPEG 数据
        guard let imageData = croppedImage.jpegData(compressionQuality: compressionQuality) else {
            completion?(.failure(ImageError.compressionFailed))
            return
        }

        do {
            // Get the storage URL
            let fileURL = storage.urlForSaving(filename: fileName)

            // Write to disk
            try imageData.write(to: fileURL)

            // Update the entity with the file path
            entity[keyPath: pathKeyPath] = fileName

            // 清除旧路径的缓存（如果有）
            if let oldPath = oldPath {
                invalidateCache(for: oldPath)
            }

            // 将新图片添加到缓存
            imageCache.setObject(croppedImage, forKey: fileName as NSString)

            // Save the model context
            try modelContext.save()
            print("✅ Saved image at: \(fileURL.path)")
            completion?(.success(fileName))
        } catch {
            print("❌ Failed to save image: \(error)")
            completion?(.failure(error))
        }
    }
    
    private func cropEdges(of image: UIImage, by pixels: Int = 1) -> UIImage {
        guard let cgImage = image.cgImage else { return image }

        let width = cgImage.width
        let height = cgImage.height

        // 确保不会裁剪到负数尺寸
        guard width > pixels * 2, height > pixels * 2 else { return image }

        let cropRect = CGRect(
            x: pixels,
            y: pixels,
            width: width - pixels * 2,
            height: height - pixels * 2
        )

        guard let croppedCGImage = cgImage.cropping(to: cropRect) else { return image }

        return UIImage(cgImage: croppedCGImage, scale: image.scale, orientation: image.imageOrientation)
    }
    
    /// Loads an image from the file system
    func loadImage(path: String?) -> UIImage? {
        guard let path = path else {
            return nil
        }

        // Check cache first
        if let cachedImage = imageCache.object(forKey: path as NSString) {
            return cachedImage
        }

        // Not in cache, load from storage
        let url = storage.resolveURL(for: path)

        do {
            // Check if file exists
            guard FileManager.default.fileExists(atPath: url.path) else {
                return nil
            }

            // Load the image data
            let imageData = try Data(contentsOf: url)

            // Create and cache the image
            if let image = UIImage(data: imageData) {
                imageCache.setObject(image, forKey: path as NSString)
                return image
            }
        } catch {
            print("❌ Failed to load image: \(error)")
        }

        return nil
    }

    /// 批量预加载图片，用于渲染优化
    func preloadImages(paths: [String?], maxConcurrent: Int = 5) async {
        let validPaths = paths.compactMap { $0 }
        let semaphore = DispatchSemaphore(value: maxConcurrent)

        await withTaskGroup(of: Void.self) { group in
            for path in validPaths {
                group.addTask {
                    semaphore.wait()
                    defer { semaphore.signal() }

                    // 检查是否已在缓存中
                    if self.imageCache.object(forKey: path as NSString) == nil {
                        if let image = await self.loadImageAsync(path: path) {
                            // 确保图片被正确缓存
                            await MainActor.run {
                                self.imageCache.setObject(image, forKey: path as NSString)
                            }
                        }
                    }
                }
            }
        }
    }

    /// 高质量图片预加载，专门用于渲染
    func preloadImagesForRendering(paths: [String?]) async {
        let validPaths = paths.compactMap { $0 }

        // 临时增加缓存限制以支持渲染
        let originalCountLimit = imageCache.countLimit
        let originalCostLimit = imageCache.totalCostLimit

        imageCache.countLimit = max(originalCountLimit, validPaths.count + 20)
        imageCache.totalCostLimit = max(originalCostLimit, 150 * 1024 * 1024) // 临时增加到150MB

        await preloadImages(paths: paths, maxConcurrent: 3)

        // 渲染完成后恢复原始限制
        DispatchQueue.main.asyncAfter(deadline: .now() + 30) { // 30秒后恢复
            self.imageCache.countLimit = originalCountLimit
            self.imageCache.totalCostLimit = originalCostLimit
        }
    }

    /// 清理缓存以释放内存
    func clearCache() {
        imageCache.removeAllObjects()
        print("🧹 Image cache cleared")
    }

    /// 清除特定路径的缓存，用于图片更新后强制重新加载
    func invalidateCache(for path: String?) {
        guard let path = path else { return }
        imageCache.removeObject(forKey: path as NSString)
        print("🔄 Cache invalidated for path: \(path)")
    }

    /// 获取缓存状态信息
    func getCacheInfo() -> (count: Int, memoryUsage: String) {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useKB]
        formatter.countStyle = .memory

        // 估算内存使用（这是一个近似值）
        let estimatedMemory = imageCache.totalCostLimit
        let memoryString = formatter.string(fromByteCount: Int64(estimatedMemory))

        return (count: imageCache.countLimit, memoryUsage: memoryString)
    }

    /// Convenience method to load image from relative path
    func loadImage(from relativePath: String) -> UIImage? {
        return loadImage(path: relativePath)
    }

    /// Saves a collection cover image and returns the file path
    func saveCollectionCoverImage(_ image: UIImage) -> String {
        let uniqueId = UUID().uuidString
        let fileName = "collection-cover-\(uniqueId).jpg"

        // Convert image to data with compression
        guard let imageData = image.jpegData(compressionQuality: compressionQuality) else {
            print("❌ Failed to compress collection cover image")
            return ""
        }

        do {
            // Get the storage URL
            let fileURL = storage.urlForSaving(filename: fileName)

            // Write to disk
            try imageData.write(to: fileURL)

            print("✅ Saved collection cover image at: \(fileURL.path)")
            return fileName
        } catch {
            print("❌ Failed to save collection cover image: \(error)")
            return ""
        }
    }
    
    func loadImageAsync(path: String) async -> UIImage? {
        // 如果已缓存，直接返回
        if let cached = imageCache.object(forKey: path as NSString) {
            return cached
        }

        let url = storage.resolveURL(for: path)
        let fileManager = FileManager.default

        do {
            // 判断是否是 iCloud 文件
            let resourceValues = try url.resourceValues(forKeys: [
                .isUbiquitousItemKey,
                .ubiquitousItemDownloadingStatusKey
            ])

            if resourceValues.isUbiquitousItem == true {
                let downloadingStatus = resourceValues.ubiquitousItemDownloadingStatus

                // 如果还没下载，手动触发下载并轮询等待
                if downloadingStatus != .current {
                    try fileManager.startDownloadingUbiquitousItem(at: url)

                    var waited = 0
                    while waited < 100 {
                        try await Task.sleep(nanoseconds: 100_000_000) // 每次等 0.1 秒
                        let newStatus = try url.resourceValues(forKeys: [.ubiquitousItemDownloadingStatusKey])
                            .ubiquitousItemDownloadingStatus
                        if newStatus == .current {
                            break
                        }
                        if Task.isCancelled {
                            return nil
                        }
                        waited += 1
                    }
                }
            }

            // 加载图片数据（放在后台线程）
            let data = try await loadDataInBackground(from: url)

            // 转换为 UIImage
            if let image = UIImage(data: data) {
                imageCache.setObject(image, forKey: path as NSString)
                return image
            }

        } catch {
            print("❌ Error loading image from iCloud path: \(path)\n\(error)")
        }

        return nil
    }
    
    func loadDataInBackground(from url: URL) async throws -> Data {
        return try await withCheckedThrowingContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                do {
                    let data = try Data(contentsOf: url)
                    continuation.resume(returning: data)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    /// Deletes an image file only (without updating entity)
    func deleteImage(path: String?) {
        guard let path = path else { return }

        // Remove from cache
        imageCache.removeObject(forKey: path as NSString)

        do {
            // Delete file
            try storage.deleteFile(at: path)
            print("✅ Deleted image: \(path)")
        } catch {
            print("❌ Failed to delete image: \(error)")
        }
    }

    /// Deletes an image and clears the entity's reference to it
    func deleteImage(path: String?, from entity: any PersistentModel, pathKeyPath: ReferenceWritableKeyPath<any PersistentModel, String?>) {
        guard let path = path else { return }

        // Remove from cache
        imageCache.removeObject(forKey: path as NSString)

        do {
            // Delete file
            try storage.deleteFile(at: path)

            // Clear reference from entity
            entity[keyPath: pathKeyPath] = nil

            // Save changes
            try modelContext.save()
            print("✅ Deleted image: \(path)")
        } catch {
            print("❌ Failed to delete image: \(error)")
        }
    }
    
    /// Checks for orphaned image files and cleans them up
    func cleanOrphanedImages(usedPaths: [String]) {
        let fileURLs = getAllImageFiles()
        let usedPathsSet = Set(usedPaths)
        
        for fileURL in fileURLs {
            let filename = fileURL.lastPathComponent
            if !usedPathsSet.contains(filename) {
                do {
                    try FileManager.default.removeItem(at: fileURL)
                    print("🗑 Deleted orphaned image: \(filename)")
                } catch {
                    print("❌ Failed to delete orphaned image: \(error)")
                }
            }
        }
    }
    
    /// Gets all image files from storage
    private func getAllImageFiles() -> [URL] {
        return storage.getAllFiles(withExtensions: ["jpg", "jpeg", "png"])
    }
    
    // MARK: - Utilities
    
    /// Returns the file size for an image
    func getFileSize(for path: String?) -> String {
        guard let path = path else { return "Unknown size" }
        let url = storage.resolveURL(for: path)
        
        do {
            let resourceValues = try url.resourceValues(forKeys: [.fileSizeKey])
            if let size = resourceValues.fileSize {
                let sizeInKB = Double(size) / 1024.0
                if sizeInKB < 1024 {
                    return String(format: "%.1f KB", sizeInKB)
                } else {
                    let sizeInMB = sizeInKB / 1024.0
                    return String(format: "%.2f MB", sizeInMB)
                }
            }
        } catch {
            print("❌ Failed to get file size: \(error)")
        }
        return "Unknown size"
    }
    
    /// Resizes an image to a specified maximum size
    func resizeImage(_ image: UIImage, maxDimension: CGFloat) -> UIImage {
        let originalSize = image.size
        var newSize = originalSize
        
        // Calculate the new size that maintains aspect ratio
        if originalSize.width > maxDimension || originalSize.height > maxDimension {
            let widthRatio = maxDimension / originalSize.width
            let heightRatio = maxDimension / originalSize.height
            let ratio = min(widthRatio, heightRatio)
            
            newSize = CGSize(width: originalSize.width * ratio, height: originalSize.height * ratio)
        }
        
        // Return original if no resize needed
        if newSize == originalSize {
            return image
        }
        
        // Render the new image
        let format = UIGraphicsImageRendererFormat()
        format.scale = 1
        let renderer = UIGraphicsImageRenderer(size: newSize, format: format)
        
        return renderer.image { context in
            image.draw(in: CGRect(origin: .zero, size: newSize))
        }
    }
    
    // MARK: - Factory Methods
    
    static func defaultStorageProvider() -> ImageStorageProvider {
        let icloudManager = iCloudDocumentManager.shared
        return icloudManager.icloudAvailable ? ICloudImageStorageProvider() : LocalImageStorageProvider()
    }
    
    // MARK: - Error Types
    
    enum ImageError: Error {
        case compressionFailed
        case fileNotFound
        case loadingFailed
    }
    
    // MARK: - Migration Helper
    
    /// 将现有任务的二进制图片数据迁移到文件系统
    func migrateExistingCoverImages() {
        do {
            // 查询所有有 coverImage 但没有 coverImagePath 的任务项
            let descriptor = FetchDescriptor<TaskItem>(
                predicate: #Predicate { $0.coverImage != nil && $0.coverImagePath == nil }
            )
            
            let taskItems = try modelContext.fetch(descriptor)
            print("发现 \(taskItems.count) 个需要迁移图片的任务")
            
            for taskItem in taskItems {
                if let imageData = taskItem.coverImage, let image = UIImage(data: imageData) {
                    // 调整图片大小
                    let resizedImage = resizeImage(image, maxDimension: 1000)
                    
                    // 保存到文件系统
                    saveImage(resizedImage, for: taskItem, pathKeyPath: \TaskItem.coverImagePath) { result in
                        switch result {
                        case .success(let path):
                            print("✅ 成功迁移图片: \(taskItem.pieceName), 路径: \(path)")
                            // 清除原始二进制数据以节省空间
                            DispatchQueue.main.async {
                                taskItem.coverImage = nil
                                try? self.modelContext.save()
                                print("✅ 已清除原始二进制数据")
                            }
                            
                        case .failure(let error):
                            print("❌ 迁移图片失败: \(taskItem.pieceName), 错误: \(error)")
                        }
                    }
                }
            }
        } catch {
            print("❌ 查询需要迁移的任务失败: \(error)")
        }
    }
    
    /// 清理已经迁移但仍保留二进制数据的任务项
    func cleanupRedundantBinaryData() {
        do {
            // 查询所有同时具有 coverImage 和 coverImagePath 的任务项
            let descriptor = FetchDescriptor<TaskItem>(
                predicate: #Predicate { $0.coverImage != nil && $0.coverImagePath != nil }
            )
            
            let taskItems = try modelContext.fetch(descriptor)
            print("发现 \(taskItems.count) 个任务同时存在二进制图片和文件路径")
            
            if taskItems.isEmpty {
                return
            }
            
            // 创建一个批处理组
            let dispatchGroup = DispatchGroup()
            
            for taskItem in taskItems {
                dispatchGroup.enter()
                
                // 验证文件实际存在
                if let path = taskItem.coverImagePath, loadImage(path: path) != nil {
                    // 文件存在且可加载，可以安全地清除二进制数据
                    DispatchQueue.main.async {
                        taskItem.coverImage = nil
                        dispatchGroup.leave()
                    }
                } else {
                    // 文件不存在或无法加载，保留二进制数据并重新迁移
                    if let imageData = taskItem.coverImage, let image = UIImage(data: imageData) {
                        let resizedImage = resizeImage(image, maxDimension: 1000)
                        
                        saveImage(resizedImage, for: taskItem, pathKeyPath: \TaskItem.coverImagePath) { result in
                            switch result {
                            case .success:
                                DispatchQueue.main.async {
                                    taskItem.coverImage = nil
                                }
                            case .failure:
                                // 迁移失败，保留二进制数据
                                break
                            }
                            dispatchGroup.leave()
                        }
                    } else {
                        dispatchGroup.leave()
                    }
                }
            }
            
            // 所有操作完成后保存上下文
            dispatchGroup.notify(queue: .main) {
                do {
                    try self.modelContext.save()
                    print("✅ 已清理冗余二进制数据")
                } catch {
                    print("❌ 保存上下文失败: \(error)")
                }
            }
        } catch {
            print("❌ 查询任务失败: \(error)")
        }
    }
}

// MARK: - Storage Providers

protocol ImageStorageProvider {
    func urlForSaving(filename: String) -> URL
    func deleteFile(at relativePath: String) throws
    func resolveURL(for relativePath: String) -> URL
    func getAllFiles(withExtensions extensions: [String]) -> [URL]
}

class ICloudImageStorageProvider: ImageStorageProvider {
    private let manager = iCloudDocumentManager.shared
    private let folderName = "Images"
    
    func urlForSaving(filename: String) -> URL {
        let folderURL = manager.urlForSaving(filename: folderName)
        
        // Create folder if it doesn't exist
        try? FileManager.default.createDirectory(
            at: folderURL,
            withIntermediateDirectories: true,
            attributes: nil
        )
        
        return folderURL.appendingPathComponent(filename)
    }
    
    func deleteFile(at relativePath: String) throws {
        let url = resolveURL(for: relativePath)
        try FileManager.default.removeItem(at: url)
    }
    
    func resolveURL(for relativePath: String) -> URL {
        let folderURL = manager.urlForSaving(filename: folderName)
        return folderURL.appendingPathComponent(relativePath)
    }
    
    func getAllFiles(withExtensions extensions: [String]) -> [URL] {
        let folderURL = manager.urlForSaving(filename: folderName)
        
        do {
            let files = try FileManager.default.contentsOfDirectory(
                at: folderURL,
                includingPropertiesForKeys: nil
            )
            
            return files.filter { file in
                let fileExtension = file.pathExtension.lowercased()
                return extensions.contains(fileExtension)
            }
        } catch {
            print("❌ Failed to get image files: \(error)")
            return []
        }
    }
}

class LocalImageStorageProvider: ImageStorageProvider {
    private let fileManager = FileManager.default
    private let folderName = "Images"
    
    private var baseURL: URL {
        let url = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
            .appendingPathComponent(folderName)
        
        try? fileManager.createDirectory(
            at: url,
            withIntermediateDirectories: true,
            attributes: nil
        )
        
        return url
    }
    
    func urlForSaving(filename: String) -> URL {
        return baseURL.appendingPathComponent(filename)
    }
    
    func deleteFile(at relativePath: String) throws {
        let url = baseURL.appendingPathComponent(relativePath)
        try fileManager.removeItem(at: url)
    }
    
    func resolveURL(for relativePath: String) -> URL {
        return baseURL.appendingPathComponent(relativePath)
    }
    
    func getAllFiles(withExtensions extensions: [String]) -> [URL] {
        do {
            let files = try fileManager.contentsOfDirectory(
                at: baseURL,
                includingPropertiesForKeys: nil
            )
            
            return files.filter { file in
                let fileExtension = file.pathExtension.lowercased()
                return extensions.contains(fileExtension)
            }
        } catch {
            print("❌ Failed to get image files: \(error)")
            return []
        }
    }
}
