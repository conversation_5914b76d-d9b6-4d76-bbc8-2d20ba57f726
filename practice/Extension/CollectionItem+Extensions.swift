//
//  CollectionItem+Extensions.swift
//  practice
//
//  Created by Augment Agent on 2024/12/19.
//

import Foundation
import SwiftData

extension CollectionItem {
    
    // MARK: - Array Comparison Utilities
    
    /// Compares two arrays of CollectionItem for equality based on display-relevant properties
    /// This is useful for optimizing UI updates by avoiding unnecessary state changes
    static func arraysAreEqual(_ lhs: [CollectionItem], _ rhs: [CollectionItem]) -> Bool {
        // Quick check: different counts
        guard lhs.count == rhs.count else { return false }
        
        // Ensure no duplicate IDs (safe Dictionary init)
        guard Set(lhs.map(\.id)).count == lhs.count,
              Set(rhs.map(\.id)).count == rhs.count else {
            return false
        }
        
        // Create dictionaries for efficient lookup by ID
        let lhsDict = Dictionary(uniqueKeysWithValues: lhs.map { ($0.id, $0) })
        let rhsDict = Dictionary(uniqueKeysWithValues: rhs.map { ($0.id, $0) })
        
        // Check if all IDs match
        guard Set(lhsDict.keys) == Set(rhsDict.keys) else { return false }
        
        // Compare each collection's key properties
        for (id, leftCollection) in lhsDict {
            guard let rightCollection = rhsDict[id] else { return false }
            
            // Compare essential properties that would affect UI display
            if leftCollection.name != rightCollection.name ||
               leftCollection.order != rightCollection.order ||
               leftCollection.cover != rightCollection.cover ||
               leftCollection.lastModified != rightCollection.lastModified ||
               leftCollection.taskItems?.count != rightCollection.taskItems?.count {
                return false
            }
        }
        
        // Check if the order in the arrays is the same (important for display order)
        for (index, leftCollection) in lhs.enumerated() {
            if leftCollection.id != rhs[index].id {
                return false
            }
        }
        
        return true
    }
    
    // MARK: - Individual Collection Comparison
    
    /// Compares this collection with another for display-relevant equality
    func isDisplayEqual(to other: CollectionItem) -> Bool {
        return self.id == other.id &&
               self.name == other.name &&
               self.order == other.order &&
               self.cover == other.cover &&
               self.lastModified == other.lastModified &&
               self.taskItems?.count == other.taskItems?.count
    }
}
