//
//  ActionSheetWrapper.swift
//  practice
//
//  Created by <PERSON> on 2025/4/20.
//

import SwiftUI

struct ActionSheetWrapper: UIViewControllerRepresentable {
    @Binding var isPresented: Bool
    let title: String
    let message: String
    let confirmText: String
    let onConfirm: () -> Void
    let onDismiss: (() -> Void)?

    func makeUIViewController(context: Context) -> UIViewController {
        UIViewController()
    }

    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {
        guard isPresented else { return }

        let alert = UIAlertController(title: title, message: message, preferredStyle: .actionSheet)
        alert.addAction(UIAlertAction(title: confirmText, style: .default) { _ in
            onConfirm()
            isPresented = false
        })
        alert.addAction(UIAlertAction(title: String(localized: "Cancel"), style: .cancel) { _ in
            isPresented = false
            onDismiss?()
        })

        // 👇👉 Fix for iPad: set sourceView and sourceRect
        if let popover = alert.popoverPresentationController {
            popover.sourceView = uiViewController.view
            popover.sourceRect = CGRect(x: uiViewController.view.bounds.midX,
                                        y: uiViewController.view.bounds.maxY,
                                        width: 0,
                                        height: 0)
            popover.permittedArrowDirections = []
        }

        DispatchQueue.main.async {
            if uiViewController.presentedViewController == nil {
                uiViewController.present(alert, animated: true)
            }
        }
    }
}


/// 用于封装 Action Sheet 弹窗的 Modifier
struct BottomActionSheetModifier: ViewModifier {
    @Binding var isPresented: Bool
    let title: String
    let message: String
    let confirmText: String
    let onConfirm: () -> Void
    let onDismiss: (() -> Void)?

    func body(content: Content) -> some View {
        content
            .background(
                ActionSheetWrapper(
                    isPresented: $isPresented,
                    title: title,
                    message: message,
                    confirmText: confirmText,
                    onConfirm: onConfirm,
                    onDismiss: onDismiss
                )
            )
    }
}

extension View {
    /// 在 iPhone 和 iPad 上都以底部 Action Sheet 方式弹出确认框
    func bottomActionSheet(
        isPresented: Binding<Bool>,
        title: String,
        message: String,
        confirmText: String = "Yes",
        onConfirm: @escaping () -> Void,
        onDismiss: (() -> Void)? = nil
    ) -> some View {
        self.modifier(BottomActionSheetModifier(
            isPresented: isPresented,
            title: title,
            message: message,
            confirmText: confirmText,
            onConfirm: onConfirm,
            onDismiss: onDismiss
        ))
    }
}
