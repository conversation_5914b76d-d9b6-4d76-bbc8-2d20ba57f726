//
//  PracticeTimeEditView.swift
//  practice
//
//  Created by 陈昇 on 3/14/24.
//

import SwiftUI
import SwiftData

struct PracticeTimeEditView: View {
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.modelContext) private var modelContext
    @ObservedObject private var taskListManager: TaskListManager = .shared
    
    @State var selectedDate: Date?
    
    @Query private var tasks: [TaskItem]
    var task: TaskItem? {
        tasks.first { $0.id == taskID }
    }
    var taskID: PersistentIdentifier

    @State private var showSheet = false
    @State private var showDeleteAlert = false
    @State private var progressToDelete: TaskProgress?

    private let calendar = Calendar.current
    
    init(task: TaskItem) {
        self.taskID = task.id
    }

    private var sortedProgressList: [TaskProgress] {
        task?.taskProgress?.sorted { $0.date > $1.date } ?? []
    }

    var body: some View {
        List {
            Group {
                if let task = task {
                    TaskStatisticsNavigator(task: task)
                        .background(Color(.systemBackground))
                        .cornerRadius(10)
                        .padding()
                }
                ForEach(sortedProgressList, id: \.id) { progress in
                    progressRow(progress)
                }
            }
            .listRowBackground(Color(.secondarySystemBackground)) // ✅ 给 row 设置背景
            .listRowInsets(EdgeInsets())
            .listRowSeparator(.hidden)
        }
        .listStyle(.plain)
        .scrollContentBackground(.hidden)
        .background(Color.clear)
        .navigationTitle("Statistics")
        .navigationBarTitleDisplayMode(.inline)
        .background(Color(.secondarySystemBackground))
        .toolbar {
            ToolbarItem(placement: .primaryAction) {
                Button {
                    selectedDate = Date()
                    showSheet = true
                } label: {
                    Image(systemName: "plus")
                }
            }
        }
        .bottomActionSheet(
            isPresented: $showDeleteAlert,
            title: String(localized: "Confirm Deletion?"),
            message: String(localized: "This action cannot be undone. Do you want to delete it?"),
            confirmText: String(localized: "Delete"),
            onConfirm: {
                if let toDelete = progressToDelete {
                    deleteProgress(toDelete)
                    updateProgressData()
                    progressToDelete = nil
                }
            },
            onDismiss: {
                progressToDelete = nil
            }
        )
        .sheet(isPresented: $showSheet) {
            TimeModifyView(
                onConfirm: { seconds, date in
                    task?.addPracticeTime(at: date, by: seconds)
                    try? modelContext.save()
                    showSheet = false
                    updateProgressData()
                    
                }
            )
        }
    }
    
    private func progressRow(_ progress: TaskProgress) -> some View {
        HStack {
            Text(formattedDate(progress.date))
            Spacer()
            Text(getTimeString(of: progress.practiceTime ?? 0))
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(10)
        .swipeActions {
            Button {
                progressToDelete = progress
                showDeleteAlert = true
            } label: {
                Label("Delete", systemImage: "trash")
            }
            .tint(.red)
        }
        .padding(.vertical, 10)
        .padding(.horizontal)
    }

    private func formattedDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        formatter.locale = Locale.current
        return formatter.string(from: date)
    }

    private func deleteProgress(_ progress: TaskProgress) {
        modelContext.delete(progress)
        try? modelContext.save()
    }
    
    private func updateProgressData() {
        taskListManager.loadData()
    }
}

#Preview {
    PracticeTimeEditView(task: TaskItem(
        pieceName: "Nocturne Op. 09 Nr, 01",
        composerName: "Chopin",
        key: .aMinor,
        difficulty: .nine,
        beginDate: Date(),
        taskProgress: []
    ))
}
