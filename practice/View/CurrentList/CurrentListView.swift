//
//  CurrentListView.swift
//  practice
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/27.
//

import SwiftUI
import SwiftData
import WidgetKit

enum TaskSortOption: String, CaseIterable {
    case totalPracticeTime = "Total Practice Time"
    case recentPractice = "Recent Practice"

    var displayName: String {
        switch self {
        case .totalPracticeTime:
            return String(localized: "Total Practice Time")
        case .recentPractice:
            return String(localized: "Recent Practice")
        }
    }

    var iconName: String {
        switch self {
        case .totalPracticeTime:
            return "chart.bar.xaxis.ascending.badge.clock"
        case .recentPractice:
            return "calendar.badge.clock"
        }
    }
}

struct CurrentListView: View {
    @ObservedObject private var taskListManager: TaskListManager = .shared
    @ObservedObject private var membershipManager: MembershipManager = .shared
    @ObservedObject private var appGlobalStateManager: AppGlobalStateManager = .shared
    @EnvironmentObject var navManager: NavigationManager
    @EnvironmentObject var recordingManager: RecordingManager
    @Environment(\.modelContext) private var modelContext

    @State private var selectedItem: TaskItem?
    @State private var showingDeleteAlert = false
    @State private var pressedItem: TaskItem?

    @AppStorage("selectedSortOption") private var selectedSortOption: TaskSortOption = .totalPracticeTime

    @State private var isPaywallPresented: Bool = false

    // 用于处理滚动和闪烁效果
    @State private var blinkingItem: TaskItem? = nil

    // Check if there are any tasks at all (both ongoing and finished)
    private var hasAnyTasks: Bool {
        return !taskListManager.taskItems.isEmpty
    }

    private var taskItems: [TaskItem] {
        // Select items based on current list type using existing state variables
        var items: [TaskItem]
        switch appGlobalStateManager.selectedListType {
        case .ongoing:
            items = taskListManager.ongoingAndDefaultTasks
        case .finished:
            items = taskListManager.achievementTasks
        }
        // Apply sorting
        return sortTasks(items, by: selectedSortOption)
    }

    private func sortTasks(_ tasks: [TaskItem], by sortOption: TaskSortOption) -> [TaskItem] {
        switch sortOption {
        case .totalPracticeTime:
            return tasks.sorted { task1, task2 in
                let time1 = getPracticeTime(of: task1)
                let time2 = getPracticeTime(of: task2)
                return time1 > time2 // 降序排列，练习时间长的在前
            }
        case .recentPractice:
            return tasks.sorted { task1, task2 in
                return task1.mostRecentPracticeDate > task2.mostRecentPracticeDate // 降序排列，最近练习的在前
            }
        }
    }
    
    var body: some View {
        ScrollViewReader { proxy in
            List {
                Section {
                    if taskItems.count > 0 {
                        ForEach(taskItems, id: \.self) { task in
                            getInfoView(task: task)
                                .listRowBackground(Color.clear)
                                .id(task.id) // 为每个任务项设置 ID 用于滚动定位
                        }
                    } else {
                        emptyGuide
                            .background(Color(.secondarySystemBackground))
                            .listRowBackground(Color.clear)
                    }
                }
                header: {
                    if hasAnyTasks {
                        VStack(spacing: 0) {
                            Picker("Task Type", selection: $appGlobalStateManager.selectedListType) {
                                ForEach(TaskListType.allCases, id: \.self) { type in
                                    HStack(spacing: 4) {  // 控制图标和文字间距
                                        Text(type.displayName)
                                            .font(.subheadline)  // 确保文字大小一致
                                    }
                                    .tag(type)
                                }
                            }
                            .pickerStyle(.segmented)
                            .padding()
                            .shadow(color: .primary.opacity(0.1), radius: 2)
                        }
                        .listRowInsets(EdgeInsets())
                    }
                }
            }
            .onChange(of: taskItems) {
                if let focusId = appGlobalStateManager.itemToFocus {
                    handleItemFocus(focusId: focusId, scrollProxy: proxy)
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .listStyle(.plain)
            .listRowInsets(EdgeInsets())
            .bottomActionSheet(
                isPresented: $showingDeleteAlert,
                title: String(localized: "Delete"),
                message: String(localized: "Are you sure you want to delete \(selectedItem?.pieceName ?? "")?"),
                confirmText: String(localized: "Delete"),
                onConfirm: {
                    if let item = selectedItem {
                        withAnimation {
                            recordingManager.cleanOrphanRecordings(for: item)
                            taskListManager.deleteTask(taskItem: item)
                            selectedItem = nil
                            showingDeleteAlert = false
                        }
                    }
                },
                onDismiss: {
                    selectedItem = nil
                }
            )
            .background(Color(.secondarySystemBackground))
        }
        .navigationTitle("Task")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Menu {
                    Section("Sort By") {
                        ForEach(TaskSortOption.allCases, id: \.self) { option in
                            Button(action: {
                                selectedSortOption = option
                            }) {
                                HStack {
                                    Image(systemName: option.iconName)
                                    Text(option.displayName)
                                    Spacer()
                                    if selectedSortOption == option {
                                        Image(systemName: "checkmark")
                                            .foregroundColor(.accentColor)
                                            .font(.caption.weight(.semibold))
                                    }
                                }
                            }
                        }
                    }
                } label: {
                    Image(systemName: selectedSortOption.iconName)
                        .font(.caption)
                }
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    handleCreateTap()
                }) {
                    Image(systemName: "plus")
                }
            }
        }
        .sheet(isPresented: $appGlobalStateManager.showTaskListCreatNewTaskSheet, onDismiss: { selectedItem = nil }) {
            sheetContent(for: selectedItem)
        }
        .sheet(isPresented: $isPaywallPresented) {
            CustomSubscribeView()
        }
        .task {
            await membershipManager.updateMembershipStatus()
        }
        .onAppear {
            print("onAppear is running")
            // Reload widget timelines and load tasks asynchronously when view appears
            WidgetCenter.shared.reloadAllTimelines()
        }
    }

    private var toggleSwitcher: some View {
        CustomSegmentedPicker(
            selection: $appGlobalStateManager.selectedListType,
            options: TaskListType.allCases
        )
//        .frame(width: 200)
    }

    private func getInfoView(task: TaskItem) -> some View {
        TaskInfoCard(task: task)
            .listCardStyle(backgroundColor: task == blinkingItem ? Color.accentColor.opacity(0.1) : Color(.systemBackground)
            )
            .shadow(color: .primary.opacity(0.1), radius: 2)
            .listRowInsets(EdgeInsets())
            .listRowSeparator(.hidden)
            .scaleEffect(task == pressedItem ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.3), value: task == pressedItem)
            .animation(.easeInOut(duration: 0.15), value: task == blinkingItem)
            .onTapGesture {
                navManager.pushState(item: .taskDetailPage(task: task))
            }
            .contextMenu {
                Button {
                    selectedItem = task
                    appGlobalStateManager.showTaskListCreatNewTaskSheet = true
                } label: {
                    Label("Edit", systemImage: "square.and.pencil")
                }
                
                Button(role: .destructive) {
                    selectedItem = task
                    showingDeleteAlert = true
                } label: {
                    Label("Delete", systemImage: "trash")
                }
            }
            .swipeActions(edge: .trailing) {
                Button {
                    selectedItem = task
                    appGlobalStateManager.showTaskListCreatNewTaskSheet = true
                } label: {
                    Label("Edit", systemImage: "square.and.pencil.circle.fill")
                }
                .tint(.indigo)
                Button {
                    selectedItem = task
                    showingDeleteAlert = true
                } label: {
                    Label("Delete", systemImage: "trash.fill")
                }
                .tint(.red)
            }
            .padding(.vertical, 8)
            .padding(.horizontal)
    }
    
    private var imageWidth: CGFloat {
        let isIPad = UIDevice.current.userInterfaceIdiom == .pad
        let imageWidth = min(UIScreen.main.bounds.width, UIScreen.main.bounds.height)
        if isIPad {
            return imageWidth * 0.5
        } else {
            return imageWidth * 0.8
        }
    }
    
    private var emptyGuide: some View {
        VStack {
            if !hasAnyTasks || appGlobalStateManager.selectedListType == .ongoing {
                HStack {
                    Text("Create New Practice Task")
                    Image(systemName: "arrowshape.turn.up.backward.fill")
                        .rotationEffect(.degrees(90))
                }
                .foregroundColor(.gray)
                .font(.body)
                .bold()
                .padding()
                .onTapGesture {
                    handleCreateTap()
                }
                Image("TaskPlaceholder")
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: imageWidth)
                    .padding(.horizontal, 10)
                    .onTapGesture {
                        handleCreateTap()
                    }
            } else {
                Image("CompletedTaskPlaceholder")
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: imageWidth)
                    .padding(.horizontal, 10)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .listRowSeparator(.hidden)
    }
    
    private func handleCreateTap() -> Void {
        appGlobalStateManager.selectedListType = .ongoing
        let count = taskListManager.userCreateTaskCount
        if membershipManager.isPremium || count < membershipManager.limitTaskCount {
            appGlobalStateManager.showTaskListCreatNewTaskSheet = true
        } else {
            isPaywallPresented = true
        }
    }
    
    private func sheetContent(for taskItem: TaskItem?) -> some View {
        NavigationView {
            if let taskItem = taskItem {
                AddNewItemView(taskItem: taskItem) {
                    appGlobalStateManager.showTaskListCreatNewTaskSheet = false
                }.navigationBarTitleDisplayMode(.inline)
            } else {
                AddNewItemView(
                    onCreate: {
                        appGlobalStateManager.showTaskListCreatNewTaskSheet = false
                        taskListManager.loadData()
                    }
                ).navigationBarTitleDisplayMode(.inline)
            }
        }
    }

    /// 处理任务项聚焦逻辑
    /// - Parameters:
    ///   - focusId: 需要聚焦的任务 ID
    ///   - scrollProxy: ScrollViewReader 的代理
    private func handleItemFocus(focusId: PersistentIdentifier, scrollProxy: ScrollViewProxy) {
        // 查找对应的任务项
        guard let targetTask = taskItems.first(where: { $0.id == focusId }) else {
//            appGlobalStateManager.clearItemToFocus()
            return
        }

        // 滚动到目标任务
        withAnimation(.easeInOut(duration: 0.5)) {
            scrollProxy.scrollTo(targetTask.id, anchor: .center)
        }

        // 延迟执行闪烁效果
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            withAnimation(.easeInOut(duration: 0.3)) {
                blinkingItem = targetTask
            }

            // 闪烁效果持续时间
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    blinkingItem = nil
                }

                // 清除聚焦项
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    appGlobalStateManager.clearItemToFocus()
                }
            }
        }
    }
}

// MARK: - Custom Segmented Picker
struct CustomSegmentedPicker<T: Hashable>: View {
    @Binding var selection: T
    let options: [T]
    @Namespace private var selectionAnimation

    var body: some View {
        HStack(spacing: 0) {
            ForEach(Array(options.enumerated()), id: \.element) { index, option in
                Button(action: {
                    withAnimation(.spring(duration: 0.25)) {
                        selection = option
                    }
                }) {
                    segmentContent(for: option, isSelected: selection == option)
                }
                .buttonStyle(PlainButtonStyle())
                .background(
                    // 移动的选中背景
                    Group {
                        if selection == option {
                            RoundedRectangle(cornerRadius: 6)
                                .fill(Color(.systemBackground))
                                .matchedGeometryEffect(id: "selectedSegment", in: selectionAnimation)
                        }
                    }
                )
                .animation(.easeInOut(duration: 0.3), value: selection == option)
            }
        }
        .padding(2)
        .background(Color(.systemGray5))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }

    @ViewBuilder
    private func segmentContent(for option: T, isSelected: Bool) -> some View {
        if let taskListType = option as? TaskListType {
            HStack(spacing: 4) {
                Image(systemName: taskListType.iconName)
                    .font(.caption)
                Text(taskListType.displayName)
                    .font(.subheadline)
            }
            .foregroundColor(.primary)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
        } else {
            Text("\(option)")
                .foregroundColor(.primary)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
        }
    }
}

#Preview {
    let modelContext = TaskItemSchemaV1.container
    NavigationStack {
        CurrentListView()
            .modelContainer(for: TaskItem.self, inMemory: false)
            .environmentObject(RecordingManager(modelContext: modelContext.mainContext))
            .environmentObject(NavigationManager())
    }
}
