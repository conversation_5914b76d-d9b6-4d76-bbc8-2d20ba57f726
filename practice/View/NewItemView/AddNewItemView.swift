//
//  AddNewItemView.swift
//  practice
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/27.
//

import SwiftUI
import PhotosUI

enum EditMode {
    case Add, Edit
}

struct AddNewItemView: View {
    @ObservedObject private var taskListManager: TaskListManager = .shared
    let originalTaskItem: TaskItem?

    // Draft state - these are the values being edited
    @State private var draftPieceName: String = ""
    @State private var draftComposerName: String = ""
    @State private var draftBeginDate: Date = Date()
    @State private var draftFinishedDate: Date = Date()
    @State var selectedImage: PhotosPickerItem?
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var imageManager: ImageManager
    @State private var coverImage: UIImage?
    @State private var originalCoverImagePath: String?
    @State private var isCoverImageModified: Bool = false

    var onCreate: () -> () = {}
    var onSave: () -> () = {}
    private var mode = EditMode.Add

    private var taskType = TaskType.Ongoing
    
    private var isCreateDisabled: Bool {
        draftPieceName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    init(taskItem: TaskItem, onSave: @escaping () -> ()) {
        self.originalTaskItem = taskItem
        self.onSave = onSave
        self.taskType = taskItem.taskType ?? .Ongoing
        self.mode = EditMode.Edit

        // Initialize draft values with existing task data
        self._draftPieceName = State(initialValue: taskItem.pieceName)
        self._draftComposerName = State(initialValue: taskItem.composerName)
        self._draftBeginDate = State(initialValue: taskItem.beginDate)
        self._draftFinishedDate = State(initialValue: taskItem.finishedDate)
        self._originalCoverImagePath = State(initialValue: taskItem.coverImagePath)
    }

    init(onCreate: @escaping () -> (), taskType: TaskType = .Ongoing) {
        self.originalTaskItem = nil
        self.onCreate = onCreate
        self.taskType = taskType
        self.mode = EditMode.Add
    }
    
    var body: some View {
        VStack(spacing: 0) {
            ZStack {
                Text(mode == .Add ? "Add New Task" : "Edit Task")
                    .font(.title2)
                    .fontWeight(.semibold)

                HStack {
                    Button("Cancel") {
                        cancelChanges()
                    }
                    Spacer()
                    Button(mode == .Add ? "Create" : "Save") {
                        if mode == .Add {
                            createTask()
                        } else {
                            saveTask()
                        }
                    }
                    .disabled(isCreateDisabled)
                }
            }
            .padding()
            .background(Color(.systemBackground))

            // Form Content
            ScrollView {
                VStack(spacing: 24) {
                    // Task Name Section
                    taskNameSection
                        .padding(.horizontal)

                    // Composer Name Section
                    composerNameSection
                        .padding(.horizontal)

                    // Date Section
                    dateSection
                        .padding(.horizontal)

                    // Cover Image Section
                    coverImageSection
                        .padding(.horizontal)
                }
                .padding(.vertical)
            }
        }
        .onChange(of: selectedImage) {
            Task {
                if let imageData = try await selectedImage?.loadTransferable(type: Data.self),
                   let uiImage = UIImage(data: imageData) {
                    // 处理图片尺寸
                    let resizedImage = imageManager.resizeImage(uiImage, maxDimension: 1000)

                    // 在UI上立即显示图片（但不保存到模型）
                    coverImage = resizedImage
                    // 标记图片已被修改
                    isCoverImageModified = true
                }
            }
        }
        .onAppear {
            // 加载现有图片（如果有）
            if let imagePath = originalCoverImagePath {
                Task {
                    coverImage = await imageManager.loadImageAsync(path: imagePath)
                }
            }
        }
    }

    private var taskNameSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Piece Name")
                .font(.headline)
                .foregroundColor(.primary)

            TextField("Enter piece name", text: $draftPieceName)
                .padding()
                .background(Color.accentColor.opacity(0.1))
                .cornerRadius(10)
        }
    }

    private var composerNameSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Composer Name")
                .font(.headline)
                .foregroundColor(.primary)

            TextField("Enter composer name", text: $draftComposerName)
                .padding()
                .background(Color.accentColor.opacity(0.1))
                .cornerRadius(10)
        }
    }

    private var dateSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Dates")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(spacing: 12) {
                HStack {
                    Image(systemName: "calendar")
                        .foregroundColor(.accentColor)
                        .frame(width: 20)
                    Text("Start Date")
                        .font(.subheadline)
                        .foregroundColor(.primary)
                    Spacer()
                    DatePicker("", selection: $draftBeginDate, displayedComponents: .date)
                        .labelsHidden()
                }
                .padding()
                .background(Color.accentColor.opacity(0.1))
                .cornerRadius(10)

                if taskType == .Achievement {
                    HStack {
                        Image(systemName: "calendar.badge.checkmark")
                            .foregroundColor(.accentColor)
                            .frame(width: 20)
                        Text("Finished Date")
                            .font(.subheadline)
                            .foregroundColor(.primary)
                        Spacer()
                        DatePicker("", selection: $draftFinishedDate, displayedComponents: .date)
                            .labelsHidden()
                    }
                    .padding()
                    .background(Color.accentColor.opacity(0.1))
                    .cornerRadius(10)
                }
            }
        }
    }

    private var coverImageSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Cover Image")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
            }

            VStack(spacing: 0) {
                PhotosPicker(
                    selection: $selectedImage,
                    matching: .images,
                    photoLibrary: .shared()
                ) {
                    if let coverImage = coverImage {
                        // full width with 3:2 aspect ratio
                        Image(uiImage: coverImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(maxWidth: .infinity, maxHeight: 160)
                            .clipShape(RoundedRectangle(cornerRadius: 10))
                            .overlay(alignment: .topTrailing) {
                                Button(action: {
                                    self.coverImage = nil
                                    selectedImage = nil
                                    // 标记图片已被修改（删除也是一种修改）
                                    isCoverImageModified = true
                                }) {
                                    Image(systemName: "minus.circle.fill")
                                        .font(.title3)
                                        .foregroundColor(.red)
                                        .background(Color.white)
                                        .clipShape(Circle())
                                }
                                .padding(6) // 控制右上角内边距
                            }
                    } else {
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(style: StrokeStyle(lineWidth: 1, dash: [6]))
                            .foregroundColor(Color.accentColor.opacity(0.5))
                            .frame(maxWidth: .infinity, minHeight: 160)
                            .overlay {
                                Image(systemName: "photo")
                                    .font(.system(size: 50))
                                    .foregroundColor(Color.accentColor.opacity(0.5))
                            }
                    }
                }
            }
        }
    }

    // MARK: - Actions
    private func createTask() {
        // 创建新的 TaskItem 使用草稿数据
        let newTaskItem = TaskItem(
            pieceName: draftPieceName.trimmingCharacters(in: .whitespacesAndNewlines),
            composerName: draftComposerName.trimmingCharacters(in: .whitespacesAndNewlines),
            key: .cMajor,
            difficulty: .one,
            beginDate: draftBeginDate
        )

        if taskType == .Achievement {
            newTaskItem.finishedDate = draftFinishedDate
            newTaskItem.taskType = .Achievement
        } else {
            newTaskItem.taskType = .Ongoing
        }

        // 先保存模型到上下文
        taskListManager.createNewTask(newTaskItem)

        // 然后保存图片（如果有）
        if let image = coverImage {
            imageManager.saveImage(image, for: newTaskItem, pathKeyPath: \TaskItem.coverImagePath)
        }
        // 完成创建
        onCreate()
        // 设置新创建的任务为聚焦项
        AppGlobalStateManager.shared.setItemToFocus(newTaskItem.id)
    }

    private func saveTask() {
        guard let taskItem = originalTaskItem else { return }

        // 应用草稿数据到原始任务
        taskItem.pieceName = draftPieceName.trimmingCharacters(in: .whitespacesAndNewlines)
        taskItem.composerName = draftComposerName.trimmingCharacters(in: .whitespacesAndNewlines)
        taskItem.beginDate = draftBeginDate

        if taskType == .Achievement {
            taskItem.finishedDate = draftFinishedDate
        }

        // 只有当图片被修改时才处理图片保存
        if isCoverImageModified {
            // 删除原有图片文件（如果存在）
            if let originalPath = originalCoverImagePath {
                imageManager.deleteImage(path: originalPath)
            }

            // 保存新图片（如果有）
            if let image = coverImage {
                imageManager.saveImage(image, for: taskItem, pathKeyPath: \TaskItem.coverImagePath)
                // 触发全局图片刷新
                AppGlobalStateManager.shared.triggerImageRefresh()
            } else {
                // 如果图片被删除，清空路径
                taskItem.coverImagePath = nil
                // 清除缓存
                if let originalPath = originalCoverImagePath {
                    imageManager.invalidateCache(for: originalPath)
                }
                // 触发全局图片刷新
                AppGlobalStateManager.shared.triggerImageRefresh()
            }
        }

        // 完成保存
        onSave()
    }

    private func cancelChanges() {
        // 如果有新选择的图片但还没保存，清理临时状态
        coverImage = nil
        selectedImage = nil
        isCoverImageModified = false
        dismiss()
    }
}

struct ItemLabel: View {
    var labelName: String
    
    var body: some View {
        Text(labelName)
            .font(.title2)
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
            .multilineTextAlignment(.leading)
    }
}

#Preview {
    AddNewItemView(taskItem: TaskItem(
        pieceName: "",
        composerName: "",
        key: .cMajor,
        difficulty: .one,
        beginDate: Date()
    ), onSave: {}).modelContainer(for: TaskItem.self, inMemory: false)
}
