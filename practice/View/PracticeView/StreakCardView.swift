//
//  StreakCardView.swift
//  practice
//
//  Created by <PERSON> on 2025/5/19.
//

import SwiftUI
import Lottie

struct StreakCardView: View {
    @ObservedObject private var taskListManager: TaskListManager = .shared
    @State private var streakCount: Int = 0
    @State private var weeklyStreak: [WeeklyStreakDay] = []
    @State private var isOnFire = false
    
    let width: CGFloat

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            let itemWidth = min(width / 13, 60)
            let paddingWidth: CGFloat = 10
            HStack(alignment: .bottom, spacing: 0) {
                VStack (alignment: .leading, spacing: 0) {
                    Text("\(streakCount)")
                        .font(.system(size: 40, weight: .bold, design: .rounded))
                    
                    Text("Streak Days")
                        .font(.custom("Nunito-SemiBold", size: 20))
                        .fontWeight(.bold)
                        .padding(.bottom, paddingWidth)
                }
                Spacer()
                Image(isOnFire ? "Fire" : "FireGray")
                    .resizable()
                    .scaledToFit()
                    .frame(height: 90)
            }
            
            HStack(spacing: itemWidth / 2) {
                ForEach(weeklyStreak) { day in
                    VStack(alignment: .center, spacing: paddingWidth / 2) {
                        ZStack {
                            Circle()
                                .fill(day.practiced
                                      ? Color.accentColor
                                      : Color.gray.opacity(0.1))
                                .frame(width: itemWidth, height: itemWidth)
                            
                            // ✅ 只在 practiced 为 true 时显示图标
                            if day.practiced {
                                if day.isToday {
                                    LottieView(animation: .named("check"))
                                        .playbackMode(.playing(.toProgress(1, loopMode: .playOnce)))
                                        .scaledToFit()
                                        .frame(width: itemWidth, height: itemWidth)
                                } else {
                                    Image("checkmark")
                                        .resizable()
                                        .frame(width: itemWidth, height: itemWidth)
                                }
                            }
                        }
                        
                        Text(day.weekday)
                            .font(.custom("Nunito-Bold", size: 20))
                            .foregroundColor(day.isToday ? .accentColor : .primary)
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
        .onChange(of: taskListManager.taskProgress, initial: true) {
            Task {
                await loadStreakData()
                await updateFireState()
            }
        }
    }

    @MainActor
    private func loadStreakData() async {
        print("loadStreakData")
        var calendar = Calendar.current
        calendar.firstWeekday = 1
        let today = calendar.startOfDay(for: Date())
        let practicedDates = Set(taskListManager.currentWeekProgress.map { calendar.startOfDay(for: $0.date) })
        let newStreakCount = taskListManager.getCurrentPracticeStreak()

        // 当前周 7 天数据（从周日开始）
        let weekdaySymbols = ["S", "M", "T", "W", "T", "F", "S"]
        let weekday = calendar.component(.weekday, from: today) // Sunday = 1

        // offset 从本周日开始
        let offsetToSunday = weekday - calendar.firstWeekday
        guard let startOfWeek = calendar.date(byAdding: .day, value: -offsetToSunday, to: today) else { return }

        let newWeeklyStreak: [WeeklyStreakDay] = (0..<7).compactMap { i in
            guard let date = calendar.date(byAdding: .day, value: i, to: startOfWeek) else { return nil }
            return WeeklyStreakDay(
                date: date,
                weekday: weekdaySymbols[i],
                practiced: practicedDates.contains(date),
                isToday: calendar.isDate(date, inSameDayAs: today)
            )
        }

        // 只在数据有变化时才重新赋值
        if newStreakCount != streakCount {
            streakCount = newStreakCount
        }

        if !weeklyStreakAreEqual(newWeeklyStreak, weeklyStreak) {
            weeklyStreak = newWeeklyStreak
        }
    }

    // 比较两个 WeeklyStreakDay 数组是否相等的辅助方法
    private func weeklyStreakAreEqual(_ streak1: [WeeklyStreakDay], _ streak2: [WeeklyStreakDay]) -> Bool {
        guard streak1.count == streak2.count else { return false }

        let calendar = Calendar.current

        for (day1, day2) in zip(streak1, streak2) {
            if !calendar.isDate(day1.date, inSameDayAs: day2.date) ||
               day1.weekday != day2.weekday ||
               day1.practiced != day2.practiced ||
               day1.isToday != day2.isToday {
                return false
            }
        }

        return true
    }
    
    @MainActor
     private func updateFireState() async {
         isOnFire = await taskListManager.isOnFire()
     }
    
    struct WeeklyStreakDay: Identifiable {
        let id = UUID()
        let date: Date
        let weekday: String // "M", "T", ...
        let practiced: Bool
        let isToday: Bool
    }
}

#Preview {
    StreakCardView(width: 375)
}
