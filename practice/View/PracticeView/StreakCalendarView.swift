//
//  StreakCalendarView.swift
//  practice
//
//  Created by <PERSON> on 2025/5/27.
//

import SwiftUI

struct StreakCalendarView: View {
    @State private var animateFlame = false
    @ObservedObject private var taskListManager: TaskListManager = .shared

    @State private var isOnFire = true
    @State private var currentStreak = 0
    @State private var longestStreak = 0
    
    @State private var currentDate = Date()
    @State private var completedDates: [Date] = []
    
    let width: CGFloat
    
    @MainActor
    private func updateFireState() async {
        isOnFire = await taskListManager.isOnFire()
    }
    
    @MainActor
    private func loadStreakData() async {
        let newCurrentStreak = taskListManager.getCurrentPracticeStreak()
        let newLongestStreak = taskListManager.getLongestPracticeStreak()
        let newCompletedDates = taskListManager.getMonthPracticedDateAt(date: currentDate)

        // 只在数据有变化时才重新赋值
        if newCurrentStreak != currentStreak {
            currentStreak = newCurrentStreak
        }

        if newLongestStreak != longestStreak {
            longestStreak = newLongestStreak
        }

        if !datesAreEqual(newCompletedDates, completedDates) {
            completedDates = newCompletedDates
        }
    }

    // 比较两个日期数组是否相等的辅助方法
    private func datesAreEqual(_ dates1: [Date], _ dates2: [Date]) -> Bool {
        guard dates1.count == dates2.count else { return false }

        let calendar = Calendar.current
        let sortedDates1 = dates1.sorted()
        let sortedDates2 = dates2.sorted()

        for (date1, date2) in zip(sortedDates1, sortedDates2) {
            if !calendar.isDate(date1, inSameDayAs: date2) {
                return false
            }
        }

        return true
    }
    
    private var calendar: Calendar {
        Calendar.current
    }
    
    private var monthYearFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM yyyy"
        return formatter
    }
    
    private var daysPracticed: Int {
        let startOfMonth = calendar.dateInterval(of: .month, for: currentDate)?.start ?? currentDate
        let endOfMonth = calendar.dateInterval(of: .month, for: currentDate)?.end ?? currentDate
        
        var count = 0
        var date = startOfMonth
        
        while date < endOfMonth {
            if completedDates.contains(where: { calendar.isDate($0, inSameDayAs: date) }) {
                count += 1
            }
            date = calendar.date(byAdding: .day, value: 1, to: date) ?? date
        }
        
        return count
    }
    
    var body: some View {
        ScrollView(.vertical) {
            let itemWidth = min(width / 13, 60)
            let paddingWidth: CGFloat = 10
            HStack {
                Group {
                    if isOnFire {
                        Text("You are on Fire!")
                    } else {
                        Text("Practice and start a new streak.")
                    }
                }
                .font(.custom("Nunito-Bold", size: 20))
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            VStack(alignment: .leading, spacing: 0) {
                HStack {
                    VStack(alignment: .leading, spacing: 0) {
                        Text("\(currentStreak)")
                            .font(.system(size: 40, weight: .bold, design: .rounded))
                        
                        Text("Streak Days")
                            .font(.custom("Nunito-SemiBold", size: 20))
                    }
                    
                    Spacer()
                    
                    Image(isOnFire ? "Fire" : "FireGray")
                        .resizable()
                        .scaledToFit()
                        .frame(height: 90)
                        .offset(y: animateFlame && isOnFire ? -5 : 0)
                        .animation(
                            isOnFire ?
                            Animation.easeInOut(duration: 1.0)
                                .repeatForever(autoreverses: true)
                            : .default,
                            value: animateFlame
                        )
                        .onAppear {
                            if isOnFire {
                                animateFlame = true
                            }
                        }
                        .onChange(of: isOnFire) {
                            if isOnFire {
                                animateFlame = true
                            } else {
                                animateFlame = false
                            }
                        }
                }
                Text("Your longest Streak is \(longestStreak) days")
                    .font(.custom("Nunito-Bold", size: 20))
                    .lineLimit(2)
                    .foregroundStyle(.gray)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 20)
            .background(Color(.systemBackground))
            .cornerRadius(10)
            .padding(.horizontal, 20)
            .padding(.bottom, paddingWidth)
            
            // Month navigation
            VStack(spacing: paddingWidth) {
                HStack {
                    VStack(alignment: .leading, spacing: 0) {
                        HStack {
                            Text(monthYearFormatter.string(from: currentDate))
                                .font(.custom("Nunito-Bold", size: 20))
                            Spacer()
                            HStack {
                                Button(action: previousMonth) {
                                    Image(systemName: "chevron.left")
                                        .font(.caption)
                                        .foregroundColor(.pink)
                                        .frame(width: itemWidth, height: itemWidth)
                                        .background(Color.accentColor.opacity(0.1))
                                        .clipShape(Circle())
                                }
                                Button(action: nextMonth) {
                                    Image(systemName: "chevron.right")
                                        .font(.caption)
                                        .foregroundColor(.pink)
                                        .frame(width: itemWidth, height: itemWidth)
                                        .background(Color.accentColor.opacity(0.1))
                                        .clipShape(Circle())
                                }
                                .disabled(!canGoToNextMonth)
                            }
                        }
                        Text("\(daysPracticed) days practiced")
                            .font(.custom("Nunito-Bold", size: 20))
                            .foregroundColor(.gray)
                    }
                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.bottom, paddingWidth)
                
                // Weekday headers
                HStack(spacing: itemWidth / 2) {
                    ForEach(["S", "M", "T", "W", "T", "F", "S"], id: \.self) { day in
                        Text(day)
                            .font(.custom("Nunito-Bold", size: 20))
                            .frame(width: itemWidth)
                    }
                }
                .padding(.bottom, 0)
                
                // Calendar Grid
                LazyVGrid(columns: Array(repeating: GridItem(.fixed(itemWidth), spacing: itemWidth / 2), count: 7), spacing: itemWidth / 2) {
                    ForEach(generateCalendarDays(), id: \.self) { day in
                            CalendarDayView(
                                dayNumber: calendar.component(.day, from: day),
                                isCompleted: completedDates.contains(where: { calendar.isDate($0, inSameDayAs: day)}),
                                isCurrentMonth: calendar.isDate(day, equalTo: currentDate, toGranularity: .month),
                                isToday: calendar.isDate(day, inSameDayAs: Date()),
                                width: itemWidth
                            )
                    }
                }
            }
            .padding(.vertical, 20)
            .background(Color(.systemBackground))
            .cornerRadius(10)
            .padding(.horizontal, 20)
        }
        .background(Color(.secondarySystemBackground))
        .onChange(of: taskListManager.taskProgress, initial: true) {
            Task {
                await loadStreakData()
                await updateFireState()
            }
        }
        .onChange(of: currentDate) {
            completedDates = []
            Task {
                let data = taskListManager.getMonthPracticedDateAt(date: currentDate)
                await MainActor.run {
                    completedDates = data
                }
            }
        }
    }
    
    private func generateCalendarDays() -> [Date] {
        guard let monthInterval = calendar.dateInterval(of: .month, for: currentDate),
              let firstDayOfMonth = calendar.date(from: calendar.dateComponents([.year, .month], from: currentDate)) else {
            return []
        }

        let firstWeekday = calendar.component(.weekday, from: firstDayOfMonth) - calendar.firstWeekday
        let leadingDays = (firstWeekday + 7) % 7 // 防止负数，统一从周日开始

        // 获取这个月的天数
        guard let range = calendar.range(of: .day, in: .month, for: currentDate) else {
            return []
        }
        let daysInMonth = range.count

        var days: [Date] = []

        // 添加前一个月的日期
        if let previousMonth = calendar.date(byAdding: .month, value: -1, to: currentDate),
           let previousMonthRange = calendar.range(of: .day, in: .month, for: previousMonth),
           let startOfPreviousMonth = calendar.date(from: calendar.dateComponents([.year, .month], from: previousMonth)) {

            let previousMonthDays = previousMonthRange.count
            for i in 0..<leadingDays {
                let day = previousMonthDays - leadingDays + i
                if let date = calendar.date(byAdding: .day, value: day, to: startOfPreviousMonth) {
                    days.append(date)
                }
            }
        }

        // 添加当前月的日期
        for i in 0..<daysInMonth {
            if let date = calendar.date(byAdding: .day, value: i, to: firstDayOfMonth) {
                days.append(date)
            }
        }

        // 添加下一个月的日期
        let remaining = 42 - days.count
        if let nextMonth = calendar.date(byAdding: .month, value: 1, to: currentDate),
           let startOfNextMonth = calendar.date(from: calendar.dateComponents([.year, .month], from: nextMonth)) {

            for i in 0..<remaining {
                if let date = calendar.date(byAdding: .day, value: i, to: startOfNextMonth) {
                    days.append(date)
                }
            }
        }

        return days
    }
    
    private var canGoToNextMonth: Bool {
        let nextMonth = calendar.date(byAdding: .month, value: 1, to: currentDate) ?? currentDate
        return nextMonth <= Date()
    }
    
    private func previousMonth() {
        currentDate = calendar.date(byAdding: .month, value: -1, to: currentDate) ?? currentDate
    }
    
    private func nextMonth() {
        currentDate = calendar.date(byAdding: .month, value: 1, to: currentDate) ?? currentDate
    }
}

struct CalendarDayView: View {
    let dayNumber: Int
    let isCompleted: Bool
    let isCurrentMonth: Bool
    let isToday: Bool
    let width: CGFloat
    
    var body: some View {
        VStack(spacing: 4) {
            // Checkmark circle
            ZStack {
                Circle()
                    .fill(circleColor)
                    .frame(width: width, height: width)
                
                if isCompleted {
                    Image("checkmark")
                        .resizable()
                        .frame(width: width, height: width)
                }
            }
            
            // Day number
            Text("\(dayNumber)")
                .font(.system(size: 12, weight: .medium, design: .rounded))
                .fontWeight(.bold)
                .foregroundColor(textColor)
        }
    }
    
    private var circleColor: Color {
        if !isCurrentMonth {
            return Color.clear
        } else if isCompleted {
            return Color.accentColor
        } else {
            return Color.gray.opacity(0.1)
        }
    }
    
    private var textColor: Color {
        if !isCurrentMonth {
            return Color.clear
        } else if isToday {
            return Color.accentColor
        } else {
            return Color.primary
        }
    }
}

#Preview {
    StreakCalendarView(width: 375)
}
