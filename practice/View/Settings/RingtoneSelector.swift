//
//  RingtoneSelector.swift
//  practice
//
//  Created by <PERSON> on 2025/6/19.
//

import SwiftUI

struct RingtoneSelector: View {
    @Binding var selectedRingtone: NotificationManager.RingtoneOption
    @ObservedObject private var notificationManager = NotificationManager.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                ForEach(NotificationManager.RingtoneOption.allCases, id: \.self) { ringtone in
                    HStack {
                        VStack(alignment: .leading) {
                            Text(ringtone.displayName)
                                .font(.body)
                        }
                        
                        Spacer()
                        
                        // 选中标记
                        Image(systemName: selectedRingtone == ringtone ? "checkmark.circle" : "circle")
                            .font(.title2)
                            .foregroundColor(selectedRingtone == ringtone ? .green : .gray)
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        selectedRingtone = ringtone
                        notificationManager.playTestSound()
                    }
                }
            }
            .navigationTitle("Select Ringtone")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    @Previewable @State var selectedRingtone = NotificationManager.RingtoneOption.morning
    RingtoneSelector(selectedRingtone: $selectedRingtone)
}
